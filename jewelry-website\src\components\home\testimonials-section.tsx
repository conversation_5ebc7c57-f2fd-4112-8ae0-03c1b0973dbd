'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Retail Customer',
    avatar: '/placeholder-avatar-1.jpg',
    rating: 5,
    content: 'Absolutely stunning jewelry! The quality exceeded my expectations and the customer service was exceptional. My engagement ring is perfect and I get compliments every day.',
    location: 'New York, NY'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'B2B Partner',
    company: 'Elegant Jewelry Store',
    avatar: '/placeholder-avatar-2.jpg',
    rating: 5,
    content: 'Working with LuxuryJewels has transformed our business. Their wholesale pricing and quality products have helped us increase our profit margins significantly.',
    location: 'Los Angeles, CA'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Retail Customer',
    avatar: '/placeholder-avatar-3.jpg',
    rating: 5,
    content: 'I purchased a necklace set for my wedding and it was absolutely perfect. The craftsmanship is incredible and the shipping was fast and secure.',
    location: 'Miami, FL'
  },
  {
    id: 4,
    name: 'David Thompson',
    role: 'B2B Partner',
    company: 'Thompson Jewelers',
    avatar: '/placeholder-avatar-4.jpg',
    rating: 5,
    content: 'The B2B portal is user-friendly and the dedicated account manager has been incredibly helpful. Great selection and competitive pricing.',
    location: 'Chicago, IL'
  },
  {
    id: 5,
    name: 'Lisa Wang',
    role: 'Retail Customer',
    avatar: '/placeholder-avatar-5.jpg',
    rating: 5,
    content: 'Beautiful jewelry collection with excellent customer service. The return policy gave me confidence to purchase online and I was not disappointed.',
    location: 'Seattle, WA'
  },
  {
    id: 6,
    name: 'Robert Martinez',
    role: 'B2B Partner',
    company: 'Martinez Fine Jewelry',
    avatar: '/placeholder-avatar-6.jpg',
    rating: 5,
    content: 'Reliable supplier with consistent quality. The bulk ordering process is smooth and delivery times are always met. Highly recommend for retailers.',
    location: 'Phoenix, AZ'
  }
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    )
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    )
    setIsAutoPlaying(false)
  }

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Customers Say</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Don&apos;t just take our word for it. Here&apos;s what our satisfied customers and business partners have to say about their experience with us.
          </p>
        </div>

        {/* Main Testimonial Display */}
        <div className="relative max-w-4xl mx-auto mb-8">
          <Card className="overflow-hidden bg-gradient-to-br from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 border-2">
            <CardContent className="p-8 md:p-12">
              <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                {/* Avatar and Info */}
                <div className="flex-shrink-0 text-center md:text-left">
                  <Avatar className="w-20 h-20 mx-auto md:mx-0 mb-4">
                    <AvatarImage src={testimonials[currentIndex].avatar} />
                    <AvatarFallback className="text-lg">
                      {testimonials[currentIndex].name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <h3 className="font-semibold text-lg">{testimonials[currentIndex].name}</h3>
                  <p className="text-sm text-muted-foreground">{testimonials[currentIndex].role}</p>
                  {testimonials[currentIndex].company && (
                    <p className="text-sm text-primary font-medium">{testimonials[currentIndex].company}</p>
                  )}
                  <p className="text-xs text-muted-foreground mt-1">{testimonials[currentIndex].location}</p>
                </div>

                {/* Testimonial Content */}
                <div className="flex-1">
                  <Quote className="h-8 w-8 text-primary/30 mb-4" />
                  <blockquote className="text-lg md:text-xl leading-relaxed mb-6">
                    "                    &ldquo;{testimonials[currentIndex].content}&rdquo;"
                  </blockquote>
                  <div className="flex items-center justify-center md:justify-start">
                    {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Navigation Arrows */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm"
            onClick={prevTestimonial}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm"
            onClick={nextTestimonial}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Testimonial Indicators */}
        <div className="flex justify-center space-x-2 mb-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentIndex ? 'bg-primary' : 'bg-muted-foreground/30'
              }`}
            />
          ))}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">4.9/5</div>
            <div className="text-sm text-muted-foreground">Average Rating</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">10K+</div>
            <div className="text-sm text-muted-foreground">Happy Customers</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">50+</div>
            <div className="text-sm text-muted-foreground">B2B Partners</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">99%</div>
            <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
          </div>
        </div>
      </div>
    </section>
  )
}
