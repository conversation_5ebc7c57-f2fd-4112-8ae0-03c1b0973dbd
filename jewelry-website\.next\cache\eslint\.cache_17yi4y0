[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\b2b-section.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\category-showcase.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\featured-products.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\hero-section.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\testimonials-section.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\layout\\footer.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\layout\\navbar.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\avatar.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\badge.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\button.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\card.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\dialog.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\form.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\input.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\label.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\select.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\sheet.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\sonner.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\textarea.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\contexts\\cart-context.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\lib\\supabase.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\lib\\utils.ts": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\types\\database.ts": "25"}, {"size": 1185, "mtime": 1754993274873, "results": "26", "hashOfConfig": "27"}, {"size": 564, "mtime": 1754993407221, "results": "28", "hashOfConfig": "27"}, {"size": 6405, "mtime": 1754993866692, "results": "29", "hashOfConfig": "27"}, {"size": 5186, "mtime": 1754993500435, "results": "30", "hashOfConfig": "27"}, {"size": 8261, "mtime": 1754993475826, "results": "31", "hashOfConfig": "27"}, {"size": 4273, "mtime": 1754993878872, "results": "32", "hashOfConfig": "27"}, {"size": 7988, "mtime": 1754994000712, "results": "33", "hashOfConfig": "27"}, {"size": 5546, "mtime": 1754993347689, "results": "34", "hashOfConfig": "27"}, {"size": 5278, "mtime": 1754993324962, "results": "35", "hashOfConfig": "27"}, {"size": 1419, "mtime": 1754993596160, "results": "36", "hashOfConfig": "27"}, {"size": 1631, "mtime": 1754993102730, "results": "37", "hashOfConfig": "27"}, {"size": 2123, "mtime": 1754993102609, "results": "38", "hashOfConfig": "27"}, {"size": 1989, "mtime": 1754993102630, "results": "39", "hashOfConfig": "27"}, {"size": 3982, "mtime": 1754993102745, "results": "40", "hashOfConfig": "27"}, {"size": 3759, "mtime": 1754993102688, "results": "41", "hashOfConfig": "27"}, {"size": 967, "mtime": 1754993102636, "results": "42", "hashOfConfig": "27"}, {"size": 611, "mtime": 1754993102644, "results": "43", "hashOfConfig": "27"}, {"size": 6253, "mtime": 1754993102710, "results": "44", "hashOfConfig": "27"}, {"size": 4090, "mtime": 1754993102763, "results": "45", "hashOfConfig": "27"}, {"size": 564, "mtime": 1754993257690, "results": "46", "hashOfConfig": "27"}, {"size": 759, "mtime": 1754993102724, "results": "47", "hashOfConfig": "27"}, {"size": 5194, "mtime": 1754993302438, "results": "48", "hashOfConfig": "27"}, {"size": 212, "mtime": 1754993787013, "results": "49", "hashOfConfig": "27"}, {"size": 166, "mtime": 1754993028194, "results": "50", "hashOfConfig": "27"}, {"size": 9552, "mtime": 1754993189584, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "mjdgx2", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 21, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\b2b-section.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\category-showcase.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\featured-products.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\home\\testimonials-section.tsx", ["127", "128"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\layout\\footer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\layout\\navbar.tsx", ["129"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\contexts\\cart-context.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\jwfinal\\jewelry-website\\src\\types\\database.ts", ["130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150"], [], {"ruleId": "151", "severity": 2, "message": "152", "line": 139, "column": 21, "nodeType": "153", "messageId": "154", "suggestions": "155"}, {"ruleId": "151", "severity": 2, "message": "152", "line": 139, "column": 78, "nodeType": "153", "messageId": "154", "suggestions": "156"}, {"ruleId": "157", "severity": 1, "message": "158", "line": 13, "column": 3, "nodeType": null, "messageId": "159", "endLine": 13, "endColumn": 7}, {"ruleId": "160", "severity": 2, "message": "161", "line": 50, "column": 19, "nodeType": "162", "messageId": "163", "endLine": 50, "endColumn": 22, "suggestions": "164"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 51, "column": 27, "nodeType": "162", "messageId": "163", "endLine": 51, "endColumn": 30, "suggestions": "165"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 70, "column": 20, "nodeType": "162", "messageId": "163", "endLine": 70, "endColumn": 23, "suggestions": "166"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 71, "column": 28, "nodeType": "162", "messageId": "163", "endLine": 71, "endColumn": 31, "suggestions": "167"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 90, "column": 20, "nodeType": "162", "messageId": "163", "endLine": 90, "endColumn": 23, "suggestions": "168"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 91, "column": 28, "nodeType": "162", "messageId": "163", "endLine": 91, "endColumn": 31, "suggestions": "169"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 196, "column": 29, "nodeType": "162", "messageId": "163", "endLine": 196, "endColumn": 32, "suggestions": "170"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 197, "column": 28, "nodeType": "162", "messageId": "163", "endLine": 197, "endColumn": 31, "suggestions": "171"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 198, "column": 21, "nodeType": "162", "messageId": "163", "endLine": 198, "endColumn": 24, "suggestions": "172"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 214, "column": 29, "nodeType": "162", "messageId": "163", "endLine": 214, "endColumn": 32, "suggestions": "173"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 215, "column": 29, "nodeType": "162", "messageId": "163", "endLine": 215, "endColumn": 32, "suggestions": "174"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 216, "column": 21, "nodeType": "162", "messageId": "163", "endLine": 216, "endColumn": 24, "suggestions": "175"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 232, "column": 30, "nodeType": "162", "messageId": "163", "endLine": 232, "endColumn": 33, "suggestions": "176"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 233, "column": 29, "nodeType": "162", "messageId": "163", "endLine": 233, "endColumn": 32, "suggestions": "177"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 234, "column": 22, "nodeType": "162", "messageId": "163", "endLine": 234, "endColumn": 25, "suggestions": "178"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 250, "column": 21, "nodeType": "162", "messageId": "163", "endLine": 250, "endColumn": 24, "suggestions": "179"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 266, "column": 21, "nodeType": "162", "messageId": "163", "endLine": 266, "endColumn": 24, "suggestions": "180"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 282, "column": 22, "nodeType": "162", "messageId": "163", "endLine": 282, "endColumn": 25, "suggestions": "181"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 299, "column": 24, "nodeType": "162", "messageId": "163", "endLine": 299, "endColumn": 27, "suggestions": "182"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 306, "column": 25, "nodeType": "162", "messageId": "163", "endLine": 306, "endColumn": 28, "suggestions": "183"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 313, "column": 25, "nodeType": "162", "messageId": "163", "endLine": 313, "endColumn": 28, "suggestions": "184"}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["185", "186", "187", "188"], ["189", "190", "191", "192"], "@typescript-eslint/no-unused-vars", "'User' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["193", "194"], ["195", "196"], ["197", "198"], ["199", "200"], ["201", "202"], ["203", "204"], ["205", "206"], ["207", "208"], ["209", "210"], ["211", "212"], ["213", "214"], ["215", "216"], ["217", "218"], ["219", "220"], ["221", "222"], ["223", "224"], ["225", "226"], ["227", "228"], ["229", "230"], ["231", "232"], ["233", "234"], {"messageId": "235", "data": "236", "fix": "237", "desc": "238"}, {"messageId": "235", "data": "239", "fix": "240", "desc": "241"}, {"messageId": "235", "data": "242", "fix": "243", "desc": "244"}, {"messageId": "235", "data": "245", "fix": "246", "desc": "247"}, {"messageId": "235", "data": "248", "fix": "249", "desc": "238"}, {"messageId": "235", "data": "250", "fix": "251", "desc": "241"}, {"messageId": "235", "data": "252", "fix": "253", "desc": "244"}, {"messageId": "235", "data": "254", "fix": "255", "desc": "247"}, {"messageId": "256", "fix": "257", "desc": "258"}, {"messageId": "259", "fix": "260", "desc": "261"}, {"messageId": "256", "fix": "262", "desc": "258"}, {"messageId": "259", "fix": "263", "desc": "261"}, {"messageId": "256", "fix": "264", "desc": "258"}, {"messageId": "259", "fix": "265", "desc": "261"}, {"messageId": "256", "fix": "266", "desc": "258"}, {"messageId": "259", "fix": "267", "desc": "261"}, {"messageId": "256", "fix": "268", "desc": "258"}, {"messageId": "259", "fix": "269", "desc": "261"}, {"messageId": "256", "fix": "270", "desc": "258"}, {"messageId": "259", "fix": "271", "desc": "261"}, {"messageId": "256", "fix": "272", "desc": "258"}, {"messageId": "259", "fix": "273", "desc": "261"}, {"messageId": "256", "fix": "274", "desc": "258"}, {"messageId": "259", "fix": "275", "desc": "261"}, {"messageId": "256", "fix": "276", "desc": "258"}, {"messageId": "259", "fix": "277", "desc": "261"}, {"messageId": "256", "fix": "278", "desc": "258"}, {"messageId": "259", "fix": "279", "desc": "261"}, {"messageId": "256", "fix": "280", "desc": "258"}, {"messageId": "259", "fix": "281", "desc": "261"}, {"messageId": "256", "fix": "282", "desc": "258"}, {"messageId": "259", "fix": "283", "desc": "261"}, {"messageId": "256", "fix": "284", "desc": "258"}, {"messageId": "259", "fix": "285", "desc": "261"}, {"messageId": "256", "fix": "286", "desc": "258"}, {"messageId": "259", "fix": "287", "desc": "261"}, {"messageId": "256", "fix": "288", "desc": "258"}, {"messageId": "259", "fix": "289", "desc": "261"}, {"messageId": "256", "fix": "290", "desc": "258"}, {"messageId": "259", "fix": "291", "desc": "261"}, {"messageId": "256", "fix": "292", "desc": "258"}, {"messageId": "259", "fix": "293", "desc": "261"}, {"messageId": "256", "fix": "294", "desc": "258"}, {"messageId": "259", "fix": "295", "desc": "261"}, {"messageId": "256", "fix": "296", "desc": "258"}, {"messageId": "259", "fix": "297", "desc": "261"}, {"messageId": "256", "fix": "298", "desc": "258"}, {"messageId": "259", "fix": "299", "desc": "261"}, {"messageId": "256", "fix": "300", "desc": "258"}, {"messageId": "259", "fix": "301", "desc": "261"}, "replaceWithAlt", {"alt": "302"}, {"range": "303", "text": "304"}, "Replace with `&quot;`.", {"alt": "305"}, {"range": "306", "text": "307"}, "Replace with `&ldquo;`.", {"alt": "308"}, {"range": "309", "text": "310"}, "Replace with `&#34;`.", {"alt": "311"}, {"range": "312", "text": "313"}, "Replace with `&rdquo;`.", {"alt": "302"}, {"range": "314", "text": "315"}, {"alt": "305"}, {"range": "316", "text": "317"}, {"alt": "308"}, {"range": "318", "text": "319"}, {"alt": "311"}, {"range": "320", "text": "321"}, "suggestUnknown", {"range": "322", "text": "323"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "324", "text": "325"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "326", "text": "323"}, {"range": "327", "text": "325"}, {"range": "328", "text": "323"}, {"range": "329", "text": "325"}, {"range": "330", "text": "323"}, {"range": "331", "text": "325"}, {"range": "332", "text": "323"}, {"range": "333", "text": "325"}, {"range": "334", "text": "323"}, {"range": "335", "text": "325"}, {"range": "336", "text": "323"}, {"range": "337", "text": "325"}, {"range": "338", "text": "323"}, {"range": "339", "text": "325"}, {"range": "340", "text": "323"}, {"range": "341", "text": "325"}, {"range": "342", "text": "323"}, {"range": "343", "text": "325"}, {"range": "344", "text": "323"}, {"range": "345", "text": "325"}, {"range": "346", "text": "323"}, {"range": "347", "text": "325"}, {"range": "348", "text": "323"}, {"range": "349", "text": "325"}, {"range": "350", "text": "323"}, {"range": "351", "text": "325"}, {"range": "352", "text": "323"}, {"range": "353", "text": "325"}, {"range": "354", "text": "323"}, {"range": "355", "text": "325"}, {"range": "356", "text": "323"}, {"range": "357", "text": "325"}, {"range": "358", "text": "323"}, {"range": "359", "text": "325"}, {"range": "360", "text": "323"}, {"range": "361", "text": "325"}, {"range": "362", "text": "323"}, {"range": "363", "text": "325"}, {"range": "364", "text": "323"}, {"range": "365", "text": "325"}, "&quot;", [5370, 5412], "\n                    &quot;                    ", "&ldquo;", [5370, 5412], "\n                    &ldquo;                    ", "&#34;", [5370, 5412], "\n                    &#34;                    ", "&rdquo;", [5370, 5412], "\n                    &rdquo;                    ", [5448, 5468], "&quot;\n                  ", [5448, 5468], "&ldquo;\n                  ", [5448, 5468], "&#34;\n                  ", [5448, 5468], "&rdquo;\n                  ", [1330, 1333], "unknown", [1330, 1333], "never", [1367, 1370], [1367, 1370], [1933, 1936], [1933, 1936], [1971, 1974], [1971, 1974], [2545, 2548], [2545, 2548], [2583, 2586], [2583, 2586], [5666, 5669], [5666, 5669], [5697, 5700], [5697, 5700], [5728, 5731], [5728, 5731], [6186, 6189], [6186, 6189], [6218, 6221], [6218, 6221], [6249, 6252], [6249, 6252], [6718, 6721], [6718, 6721], [6750, 6753], [6750, 6753], [6782, 6785], [6782, 6785], [7199, 7202], [7199, 7202], [7659, 7662], [7659, 7662], [8129, 8132], [8129, 8132], [8585, 8588], [8585, 8588], [8751, 8754], [8751, 8754], [8920, 8923], [8920, 8923]]