import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Building2,
  TrendingUp,
  Shield,
  Clock,
  Users,
  Package,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

const benefits = [
  {
    icon: TrendingUp,
    title: 'Wholesale Pricing',
    description: 'Competitive wholesale rates with volume discounts for bulk orders'
  },
  {
    icon: Shield,
    title: 'Quality Guarantee',
    description: 'All products come with quality certification and warranty'
  },
  {
    icon: Clock,
    title: 'Fast Processing',
    description: 'Quick order processing and expedited shipping for business needs'
  },
  {
    icon: Package,
    title: 'Bulk Orders',
    description: 'Minimum order quantities with flexible payment terms'
  }
]

const features = [
  'Dedicated account manager',
  'Custom pricing tiers',
  'Priority customer support',
  'Flexible payment terms',
  'Bulk order discounts',
  'Product customization options'
]

export function B2BSection() {
  return (
    <section className="py-16 bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div>
            <Badge variant="secondary" className="mb-4">
              <Building2 className="h-4 w-4 mr-2" />
              B2B Solutions
            </Badge>

            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Partner with Us for
              <span className="text-primary block">Wholesale Success</span>
            </h2>

            <p className="text-lg text-muted-foreground mb-8">
              Join our network of successful jewelry retailers and resellers. Get access to exclusive wholesale pricing, dedicated support, and a comprehensive product catalog designed for business growth.
            </p>

            {/* Benefits Grid */}
            <div className="grid sm:grid-cols-2 gap-4 mb-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <benefit.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">{benefit.title}</h3>
                    <p className="text-sm text-muted-foreground">{benefit.description}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/b2b/apply">
                <Button size="lg" className="w-full sm:w-auto">
                  Apply for B2B Account
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/b2b/login">
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  B2B Portal Login
                </Button>
              </Link>
            </div>
          </div>

          {/* Right Content - Features Card */}
          <div>
            <Card className="p-8 bg-white dark:bg-slate-800 shadow-lg">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-2xl font-bold mb-2">B2B Membership Benefits</h3>
                <p className="text-muted-foreground">
                  Everything you need to grow your jewelry business
                </p>
              </div>

              <div className="space-y-4 mb-8">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 pt-6 border-t">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary mb-1">50+</div>
                  <div className="text-xs text-muted-foreground">Active Partners</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary mb-1">500+</div>
                  <div className="text-xs text-muted-foreground">Products</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary mb-1">24/7</div>
                  <div className="text-xs text-muted-foreground">Support</div>
                </div>
              </div>
            </Card>

            {/* Application Process */}
            <Card className="mt-6 p-6 bg-primary/5 border-primary/20">
              <h4 className="font-semibold mb-4 text-center">Simple Application Process</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">1</div>
                  <span className="text-sm">Submit application with business details</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">2</div>
                  <span className="text-sm">Upload required business documents</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">3</div>
                  <span className="text-sm">Get approved and start ordering</span>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
