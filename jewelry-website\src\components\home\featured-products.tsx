'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useCart } from '@/contexts/cart-context'
import { createClient } from '@/lib/supabase'
import { Product } from '@/types/database'
import { ShoppingCart, Eye, Star } from 'lucide-react'
import { toast } from 'sonner'

export function FeaturedProducts() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const { addToCart } = useCart()

  useEffect(() => {
    fetchFeaturedProducts()
  }, [])

  const fetchFeaturedProducts = async () => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('is_active', true)
        .limit(8)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching products:', error)
        // For demo purposes, use mock data
        setProducts(mockProducts)
      } else {
        setProducts(data || mockProducts)
      }
    } catch (error) {
      console.error('Error:', error)
      setProducts(mockProducts)
    } finally {
      setLoading(false)
    }
  }

  const handleAddToCart = (product: Product) => {
    addToCart(product, 1, 'b2c')
    toast.success(`${product.name} added to cart!`)
  }

  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Products</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Discover our handpicked selection of premium jewelry pieces
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <Card key={i} className="overflow-hidden">
                <div className="aspect-square bg-muted animate-pulse" />
                <CardContent className="p-4">
                  <div className="h-4 bg-muted rounded animate-pulse mb-2" />
                  <div className="h-4 bg-muted rounded animate-pulse w-2/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Products</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Discover our handpicked selection of premium jewelry pieces, crafted with the finest materials and attention to detail.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {products.map((product) => (
            <Card key={product.id} className="group overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative aspect-square overflow-hidden">
                <Image
                  src={product.images?.[0] || '/placeholder-jewelry.jpg'}
                  alt={product.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Link href={`/products/${product.id}`}>
                    <Button size="icon" variant="secondary" className="h-8 w-8">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                {product.stock_quantity < 10 && product.stock_quantity > 0 && (
                  <Badge variant="destructive" className="absolute top-2 left-2">
                    Low Stock
                  </Badge>
                )}
                {product.stock_quantity === 0 && (
                  <Badge variant="secondary" className="absolute top-2 left-2">
                    Out of Stock
                  </Badge>
                )}
              </div>
              
              <CardContent className="p-4">
                <h3 className="font-semibold mb-2 line-clamp-2">{product.name}</h3>
                <div className="flex items-center mb-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <span className="text-sm text-muted-foreground ml-2">(4.8)</span>
                </div>
                <div className="flex items-center justify-between mb-3">
                  <span className="text-lg font-bold text-primary">
                    ${product.b2c_price.toFixed(2)}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    SKU: {product.sku}
                  </span>
                </div>
                <Button 
                  onClick={() => handleAddToCart(product)}
                  disabled={product.stock_quantity === 0}
                  className="w-full"
                  size="sm"
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Add to Cart
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Link href="/products">
            <Button size="lg" variant="outline">
              View All Products
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}

// Mock data for demonstration
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Diamond Solitaire Ring',
    description: 'Elegant 1-carat diamond solitaire ring in 18k white gold',
    sku: 'DSR001',
    b2c_price: 2999.99,
    b2b_price: 2399.99,
    stock_quantity: 5,
    images: ['/placeholder-jewelry.jpg'],
    category_id: null,
    weight: null,
    material: '18k White Gold',
    dimensions: null,
    min_quantity_b2b: 1,
    specifications: null,
    seo_title: null,
    seo_description: null,
    is_active: true,
    created_at: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Pearl Necklace',
    description: 'Classic freshwater pearl necklace with sterling silver clasp',
    sku: 'PN002',
    b2c_price: 599.99,
    b2b_price: 479.99,
    stock_quantity: 12,
    images: ['/placeholder-jewelry.jpg'],
    category_id: null,
    weight: null,
    material: 'Sterling Silver',
    dimensions: null,
    min_quantity_b2b: 1,
    specifications: null,
    seo_title: null,
    seo_description: null,
    is_active: true,
    created_at: new Date().toISOString()
  },
  {
    id: '3',
    name: 'Gold Tennis Bracelet',
    description: 'Stunning tennis bracelet with cubic zirconia stones',
    sku: 'GTB003',
    b2c_price: 899.99,
    b2b_price: 719.99,
    stock_quantity: 8,
    images: ['/placeholder-jewelry.jpg'],
    category_id: null,
    weight: null,
    material: '14k Gold',
    dimensions: null,
    min_quantity_b2b: 1,
    specifications: null,
    seo_title: null,
    seo_description: null,
    is_active: true,
    created_at: new Date().toISOString()
  },
  {
    id: '4',
    name: 'Sapphire Earrings',
    description: 'Beautiful blue sapphire stud earrings in white gold',
    sku: 'SE004',
    b2c_price: 1299.99,
    b2b_price: 1039.99,
    stock_quantity: 3,
    images: ['/placeholder-jewelry.jpg'],
    category_id: null,
    weight: null,
    material: '18k White Gold',
    dimensions: null,
    min_quantity_b2b: 1,
    specifications: null,
    seo_title: null,
    seo_description: null,
    is_active: true,
    created_at: new Date().toISOString()
  }
]
