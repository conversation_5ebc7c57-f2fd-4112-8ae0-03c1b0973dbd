import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { ArrowRight } from 'lucide-react'

const categories = [
  {
    id: 'rings',
    name: 'Rings',
    description: 'Engagement, wedding, and fashion rings',
    image: '/placeholder-rings.jpg',
    href: '/categories/rings',
    count: '150+ items'
  },
  {
    id: 'necklaces',
    name: 'Necklaces',
    description: 'Elegant chains and pendants',
    image: '/placeholder-necklaces.jpg',
    href: '/categories/necklaces',
    count: '200+ items'
  },
  {
    id: 'earrings',
    name: 'Earrings',
    description: 'Studs, hoops, and drop earrings',
    image: '/placeholder-earrings.jpg',
    href: '/categories/earrings',
    count: '180+ items'
  },
  {
    id: 'bracelets',
    name: 'Bracelets',
    description: 'Tennis, charm, and cuff bracelets',
    image: '/placeholder-bracelets.jpg',
    href: '/categories/bracelets',
    count: '120+ items'
  },
  {
    id: 'watches',
    name: 'Watches',
    description: 'Luxury timepieces for every occasion',
    image: '/placeholder-watches.jpg',
    href: '/categories/watches',
    count: '80+ items'
  },
  {
    id: 'sets',
    name: 'Jewelry Sets',
    description: 'Coordinated pieces for special occasions',
    image: '/placeholder-sets.jpg',
    href: '/categories/sets',
    count: '60+ items'
  }
]

export function CategoryShowcase() {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Shop by Category</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Explore our diverse collection of jewelry categories, each carefully curated to offer the finest pieces for every style and occasion.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Link key={category.id} href={category.href}>
              <Card className="group overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div className="relative aspect-[4/3] overflow-hidden">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold mb-1">{category.name}</h3>
                    <p className="text-sm text-white/80">{category.count}</p>
                  </div>
                  <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                      <ArrowRight className="h-5 w-5 text-white" />
                    </div>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                    {category.name}
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    {category.description}
                  </p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Featured Category Banner */}
        <div className="mt-16">
          <Card className="overflow-hidden bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="p-8">
                <h3 className="text-2xl md:text-3xl font-bold mb-4">
                  Bridal Collection
                </h3>
                <p className="text-muted-foreground mb-6">
                  Make your special day unforgettable with our exquisite bridal jewelry collection. From engagement rings to wedding sets, find the perfect pieces to celebrate your love story.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/categories/bridal">
                    <div className="inline-flex items-center text-primary font-medium hover:underline">
                      Explore Bridal Collection
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </div>
                  </Link>
                </div>
              </div>
              <div className="relative aspect-[4/3] md:aspect-auto md:h-64">
                <Image
                  src="/placeholder-bridal.jpg"
                  alt="Bridal Collection"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </Card>
        </div>
      </div>
    </section>
  )
}
