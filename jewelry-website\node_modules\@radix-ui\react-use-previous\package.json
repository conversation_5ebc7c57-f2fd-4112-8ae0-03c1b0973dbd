{"name": "@radix-ui/react-use-previous", "version": "1.1.1", "license": "MIT", "source": "./src/index.ts", "main": "./dist/index.js", "module": "./dist/index.mjs", "publishConfig": {"main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}}, "files": ["dist", "README.md"], "sideEffects": false, "scripts": {"lint": "eslint --max-warnings 0 src", "clean": "rm -rf dist", "version": "yarn version"}, "devDependencies": {"@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "homepage": "https://radix-ui.com/primitives", "repository": {"type": "git", "url": "git+https://github.com/radix-ui/primitives.git"}, "bugs": {"url": "https://github.com/radix-ui/primitives/issues"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "types": "./dist/index.d.ts"}