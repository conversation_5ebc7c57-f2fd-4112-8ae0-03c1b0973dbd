export interface Database {
  public: {
    Tables: {
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          image_url: string | null
          parent_id: string | null
          sort_order: number | null
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          image_url?: string | null
          parent_id?: string | null
          sort_order?: number | null
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          image_url?: string | null
          parent_id?: string | null
          sort_order?: number | null
          is_active?: boolean
          created_at?: string
        }
      }
      products: {
        Row: {
          id: string
          category_id: string | null
          name: string
          description: string | null
          sku: string
          weight: number | null
          material: string | null
          dimensions: string | null
          b2c_price: number
          b2b_price: number
          min_quantity_b2b: number
          stock_quantity: number
          images: any | null
          specifications: any | null
          seo_title: string | null
          seo_description: string | null
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          category_id?: string | null
          name: string
          description?: string | null
          sku: string
          weight?: number | null
          material?: string | null
          dimensions?: string | null
          b2c_price: number
          b2b_price: number
          min_quantity_b2b?: number
          stock_quantity?: number
          images?: any | null
          specifications?: any | null
          seo_title?: string | null
          seo_description?: string | null
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          category_id?: string | null
          name?: string
          description?: string | null
          sku?: string
          weight?: number | null
          material?: string | null
          dimensions?: string | null
          b2c_price?: number
          b2b_price?: number
          min_quantity_b2b?: number
          stock_quantity?: number
          images?: any | null
          specifications?: any | null
          seo_title?: string | null
          seo_description?: string | null
          is_active?: boolean
          created_at?: string
        }
      }
      reseller_applications: {
        Row: {
          id: string
          business_name: string
          contact_person: string
          email: string
          phone: string
          business_address: string
          tax_id: string | null
          business_license_url: string | null
          years_in_business: number | null
          estimated_orders: string | null
          references: string | null
          status: string
          admin_notes: string | null
          created_at: string
          reviewed_at: string | null
          reviewed_by: string | null
        }
        Insert: {
          id?: string
          business_name: string
          contact_person: string
          email: string
          phone: string
          business_address: string
          tax_id?: string | null
          business_license_url?: string | null
          years_in_business?: number | null
          estimated_orders?: string | null
          references?: string | null
          status?: string
          admin_notes?: string | null
          created_at?: string
          reviewed_at?: string | null
          reviewed_by?: string | null
        }
        Update: {
          id?: string
          business_name?: string
          contact_person?: string
          email?: string
          phone?: string
          business_address?: string
          tax_id?: string | null
          business_license_url?: string | null
          years_in_business?: number | null
          estimated_orders?: string | null
          references?: string | null
          status?: string
          admin_notes?: string | null
          created_at?: string
          reviewed_at?: string | null
          reviewed_by?: string | null
        }
      }
      b2b_users: {
        Row: {
          id: string
          application_id: string | null
          business_name: string
          contact_person: string
          pricing_tier: string
          credit_limit: number
          payment_terms: number
          is_active: boolean
          created_at: string
        }
        Insert: {
          id: string
          application_id?: string | null
          business_name: string
          contact_person: string
          pricing_tier?: string
          credit_limit?: number
          payment_terms?: number
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          application_id?: string | null
          business_name?: string
          contact_person?: string
          pricing_tier?: string
          credit_limit?: number
          payment_terms?: number
          is_active?: boolean
          created_at?: string
        }
      }
      b2c_orders: {
        Row: {
          id: string
          order_number: string
          customer_name: string
          customer_email: string
          customer_phone: string
          shipping_address: any
          billing_address: any | null
          products: any
          subtotal: number
          tax_amount: number
          total_amount: number
          whatsapp_sent: boolean
          whatsapp_sent_at: string | null
          status: string
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          order_number: string
          customer_name: string
          customer_email: string
          customer_phone: string
          shipping_address: any
          billing_address?: any | null
          products: any
          subtotal: number
          tax_amount?: number
          total_amount: number
          whatsapp_sent?: boolean
          whatsapp_sent_at?: string | null
          status?: string
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          order_number?: string
          customer_name?: string
          customer_email?: string
          customer_phone?: string
          shipping_address?: any
          billing_address?: any | null
          products?: any
          subtotal?: number
          tax_amount?: number
          total_amount?: number
          whatsapp_sent?: boolean
          whatsapp_sent_at?: string | null
          status?: string
          notes?: string | null
          created_at?: string
        }
      }
      b2b_orders: {
        Row: {
          id: string
          order_number: string
          user_id: string | null
          products: any
          subtotal: number
          discount_amount: number
          tax_amount: number
          total_amount: number
          payment_status: string
          delivery_status: string
          payment_terms: number | null
          due_date: string | null
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          order_number: string
          user_id?: string | null
          products: any
          subtotal: number
          discount_amount?: number
          tax_amount?: number
          total_amount: number
          payment_status?: string
          delivery_status?: string
          payment_terms?: number | null
          due_date?: string | null
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          order_number?: string
          user_id?: string | null
          products?: any
          subtotal?: number
          discount_amount?: number
          tax_amount?: number
          total_amount?: number
          payment_status?: string
          delivery_status?: string
          payment_terms?: number | null
          due_date?: string | null
          notes?: string | null
          created_at?: string
        }
      }
      admin_users: {
        Row: {
          id: string
          role: string
          permissions: any | null
          is_active: boolean
          created_at: string
        }
        Insert: {
          id: string
          role?: string
          permissions?: any | null
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          role?: string
          permissions?: any | null
          is_active?: boolean
          created_at?: string
        }
      }
    }
  }
}

export type Category = Database['public']['Tables']['categories']['Row']
export type Product = Database['public']['Tables']['products']['Row']
export type ResellerApplication = Database['public']['Tables']['reseller_applications']['Row']
export type B2BUser = Database['public']['Tables']['b2b_users']['Row']
export type B2COrder = Database['public']['Tables']['b2c_orders']['Row']
export type B2BOrder = Database['public']['Tables']['b2b_orders']['Row']
export type AdminUser = Database['public']['Tables']['admin_users']['Row']
