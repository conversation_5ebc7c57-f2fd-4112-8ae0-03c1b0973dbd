{"version": 3, "file": "PostgrestClient.d.ts", "sourceRoot": "", "sources": ["../../src/PostgrestClient.ts"], "names": [], "mappings": "AAAA,OAAO,qBAAqB,MAAM,yBAAyB,CAAA;AAC3D,OAAO,sBAAsB,MAAM,0BAA0B,CAAA;AAG7D,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,SAAS,CAAA;AAE9C;;;;;;;;;GASG;AACH,MAAM,CAAC,OAAO,OAAO,eAAe,CAClC,QAAQ,GAAG,GAAG,EACd,UAAU,SAAS,MAAM,GAAG,MAAM,QAAQ,GAAG,QAAQ,SAAS,MAAM,QAAQ,GACxE,QAAQ,GACR,MAAM,GAAG,MAAM,QAAQ,EAC3B,MAAM,SAAS,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC,SAAS,aAAa,GACrE,QAAQ,CAAC,UAAU,CAAC,GACpB,GAAG;IAEP,GAAG,EAAE,MAAM,CAAA;IACX,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAC/B,UAAU,CAAC,EAAE,UAAU,CAAA;IACvB,KAAK,CAAC,EAAE,KAAK,CAAA;IAGb;;;;;;;;OAQG;gBAED,GAAG,EAAE,MAAM,EACX,EACE,OAAY,EACZ,MAAM,EACN,KAAK,GACN,GAAE;QACD,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAChC,MAAM,CAAC,EAAE,UAAU,CAAA;QACnB,KAAK,CAAC,EAAE,KAAK,CAAA;KACT;IAQR,IAAI,CACF,SAAS,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,EACjD,KAAK,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,EACzC,QAAQ,EAAE,SAAS,GAAG,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC;IACvE,IAAI,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,SAAS,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAC1F,QAAQ,EAAE,QAAQ,GACjB,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC;IAehD;;;;;;OAMG;IACH,MAAM,CAAC,aAAa,SAAS,MAAM,GAAG,MAAM,QAAQ,EAClD,MAAM,EAAE,aAAa,GACpB,eAAe,CAChB,QAAQ,EACR,aAAa,EACb,QAAQ,CAAC,aAAa,CAAC,SAAS,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,GAAG,CAC9E;IAQD;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,GAAG,CAAC,MAAM,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,EAC3F,EAAE,EAAE,MAAM,EACV,IAAI,GAAE,EAAE,CAAC,MAAM,CAAM,EACrB,EACE,IAAY,EACZ,GAAW,EACX,KAAK,GACN,GAAE;QACD,IAAI,CAAC,EAAE,OAAO,CAAA;QACd,GAAG,CAAC,EAAE,OAAO,CAAA;QACb,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;KACrC,GACL,sBAAsB,CACvB,MAAM,EACN,EAAE,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,GACvB,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GACnD,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,GACrB,KAAK,GACP,KAAK,EACT,EAAE,CAAC,SAAS,CAAC,EACb,MAAM,EACN,IAAI,CACL;CAmCF"}