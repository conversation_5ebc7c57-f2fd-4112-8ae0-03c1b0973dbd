# Jewelry Website Development Requirements - Complete Tech Stack

## Project Overview
Build a professional dual-purpose jewelry e-commerce website catering to both B2C (Business-to-Consumer) and B2B (Business-to-Business) customers with modern architecture and seamless user experiences.

## Technology Stack

### **Recommended Tech Stack**
- **Frontend:** Next.js 15+ (React) with TypeScript
- **Backend:** Supabase (PostgreSQL + API + Storage)
- **Database:** Supabase PostgreSQL with real-time subscriptions  
- **File Storage:** Supabase Storage for images and documents
- **Styling:** Tailwind CSS with shadcn/ui components
- **Email Service:** Resend or SendGrid via Edge Functions
- **WhatsApp API:** Integration via Edge Functions
- **Hosting:** Vercel (frontend) + Supabase (backend)
- **Image Optimization:** Next.js Image component

## Core Features & User Flows

### 1. B2C (Retail Customer) Experience

#### Guest Shopping Journey
- **Product Browsing:** High-quality jewelry catalog with advanced filtering
- **Product Details:** Multiple images, zoom functionality, detailed descriptions
- **Shopping Cart:** Session-based cart with quantity management(local storage card will remain same even if refreshed)
- **Guest Checkout:** No registration required - collect customer information
- **Order Summary:** Professional invoice-style summary with product images

#### WhatsApp Integration Flow
- **Automatic Redirect:** Post-checkout redirect to WhatsApp
- **Document Generation:** PDF invoice with customer details and product images
- **Order Processing:** Handle negotiations and payments via WhatsApp chat

### 2. B2B (Wholesale/Reseller) Experience  

#### Reseller Application System
- **Application Form:** Comprehensive business registration with document uploads
- **Document Storage:** Secure storage of business licenses and tax documents
- **Admin Review:** Dashboard for reviewing and approving applications
- **Account Creation:** Automatic user provisioning upon approval
- **Email Notifications:** Automated approval/rejection communications

#### B2B User Portal
- **Secure Login:** Supabase authentication with role-based access
- **Wholesale Pricing:** Dynamic pricing based on user tier and quantities
- **Minimum Orders:** Enforce minimum quantity requirements
- **Bulk Ordering:** Enhanced cart for large quantity purchases
- **Order History:** Complete purchase history with reorder functionality
- **Account Management:** Business profile and preferences

### 3. Admin Dashboard & Management

#### Product Management
- **Category System:** Hierarchical categories with image support
- **Product Catalog:** Rich product editor with multiple images
- **Inventory Tracking:** Real-time stock levels with low-stock alerts
- **Dual Pricing:** Separate B2C retail and B2B wholesale pricing
- **SKU Management:** Automated SKU generation and barcode support
- **Bulk Operations:** Import/export products via CSV

#### User & Order Management
- **Reseller Applications:** Review queue with document viewer
- **User Management:** B2B user accounts with tier management
- **Order Processing:** Unified dashboard for B2C and B2B orders
- **Customer Support:** Order status updates and communication tools
- **Analytics Dashboard:** Sales metrics, popular products, customer insights

#### System Configuration
- **Business Settings:** WhatsApp number, email templates, tax settings
- **Pricing Rules:** Automated discounts, bulk pricing, promotional pricing
- **Email Templates:** Customizable templates for various notifications
- **API Integrations:** WhatsApp, email

## Database Architecture (Supabase PostgreSQL)

### Core Tables Schema
```sql
-- Categories
categories (
  id uuid primary key,
  name varchar not null,
  description text,
  image_url text,
  parent_id uuid references categories(id),
  sort_order integer,
  is_active boolean default true,
  created_at timestamp default now()
);

-- Products  
products (
  id uuid primary key,
  category_id uuid references categories(id),
  name varchar not null,
  description text,
  sku varchar unique not null,
  weight decimal,
  material varchar,
  dimensions varchar,
  b2c_price decimal not null,
  b2b_price decimal not null,
  min_quantity_b2b integer default 1,
  stock_quantity integer default 0,
  images jsonb,
  specifications jsonb,
  seo_title varchar,
  seo_description text,
  is_active boolean default true,
  created_at timestamp default now()
);

-- B2B Applications
reseller_applications (
  id uuid primary key,
  business_name varchar not null,
  contact_person varchar not null,
  email varchar not null,
  phone varchar not null,
  business_address text not null,
  tax_id varchar,
  business_license_url text,
  years_in_business integer,
  estimated_orders varchar,
  references text,
  status varchar default 'pending',
  admin_notes text,
  created_at timestamp default now(),
  reviewed_at timestamp,
  reviewed_by uuid
);

-- B2B Users
b2b_users (
  id uuid primary key references auth.users(id),
  application_id uuid references reseller_applications(id),
  business_name varchar not null,
  contact_person varchar not null,
  pricing_tier varchar default 'standard',
  credit_limit decimal default 0,
  payment_terms integer default 30,
  is_active boolean default true,
  created_at timestamp default now()
);

-- B2C Orders
b2c_orders (
  id uuid primary key,
  order_number varchar unique not null,
  customer_name varchar not null,
  customer_email varchar not null,
  customer_phone varchar not null,
  shipping_address jsonb not null,
  billing_address jsonb,
  products jsonb not null,
  subtotal decimal not null,
  tax_amount decimal default 0,
  total_amount decimal not null,
  whatsapp_sent boolean default false,
  whatsapp_sent_at timestamp,
  status varchar default 'pending',
  notes text,
  created_at timestamp default now()
);

-- B2B Orders
b2b_orders (
  id uuid primary key,
  order_number varchar unique not null,
  user_id uuid references b2b_users(id),
  products jsonb not null,
  subtotal decimal not null,
  discount_amount decimal default 0,
  tax_amount decimal default 0,
  total_amount decimal not null,
  payment_status varchar default 'pending',
  delivery_status varchar default 'pending',
  payment_terms integer,
  due_date date,
  notes text,
  created_at timestamp default now()
);

-- Admin Users
admin_users (
  id uuid primary key references auth.users(id),
  role varchar not null default 'admin',
  permissions jsonb,
  is_active boolean default true,
  created_at timestamp default now()
);
```

### Storage Buckets Structure
```
Supabase Storage Organization:
├── product-images/
│   ├── categories/
│   ├── products/
│   └── variants/
├── business-documents/
│   ├── licenses/
│   ├── tax-documents/
│   └── applications/
├── generated-documents/
│   ├── b2c-invoices/
│   ├── b2b-invoices/
│   └── quotes/
├── admin-uploads/
│   └── bulk-imports/
└── marketing/
    └── banners/
```

## Key Implementation Features

### Real-time Functionality
- **Live Order Updates:** Admin dashboard shows real-time order notifications
- **Inventory Sync:** Stock levels update across all user sessions
- **Application Status:** B2B applicants see real-time approval status

### Security Implementation
- **Row Level Security:** Supabase RLS policies for data protection
- **File Upload Security:** Validated and sanitized file uploads
- **API Rate Limiting:** Prevent abuse and ensure fair usage
- **Data Encryption:** All sensitive data encrypted at rest and in transit

### Performance Optimization
- **Image Optimization:** Next.js Image component with Supabase transforms
- **Caching Strategy:** Static generation for product pages, ISR for dynamic content
- **Database Indexing:** Optimized queries for product search and filtering
- **Lazy Loading:** Progressive image loading for better performance

### WhatsApp Integration Architecture
```javascript
// Supabase Edge Function for WhatsApp processing
export default async function handler(req: Request) {
  const order = await req.json();
  
  // Generate PDF invoice
  const invoice = await generatePDFInvoice(order);
  
  // Upload to Supabase Storage
  const { data: fileUpload } = await supabase.storage
    .from('generated-documents')
    .upload(`b2c-invoices/${order.id}.pdf`, invoice);
  
  // Send WhatsApp message
  const whatsappResponse = await sendWhatsAppMessage({
    to: process.env.WHATSAPP_BUSINESS_NUMBER,
    message: `New order from ${order.customer_name}`,
    document: fileUpload.publicUrl
  });
  
  // Update order status
  await supabase
    .from('b2c_orders')
    .update({ 
      whatsapp_sent: true, 
      whatsapp_sent_at: new Date().toISOString() 
    })
    .eq('id', order.id);
    
  return new Response(JSON.stringify({ success: true }));
}
```

### Mobile Experience
- **Progressive Web App:** Native app-like experience
- **Touch Optimized:** Jewelry gallery with pinch-to-zoom
- **Mobile Payments:** Mobile-optimized checkout flow
- **Push Notifications:** Order updates, promotional offers
- **Offline Capability:** Basic browsing without internet connection


